# 虚拟线程优雅关闭解决方案

## 问题背景

在Java应用中使用虚拟线程时，如果没有正确的生命周期管理，可能会导致：
1. 应用关闭时正在执行的虚拟线程被强制中断
2. 数据处理不完整或状态不一致
3. 资源泄漏

## 解决方案

### 1. 统一的虚拟线程池管理器

创建了 `VirtualThreadPoolManager` 组件来统一管理所有虚拟线程池：

```java
@Component
public class VirtualThreadPoolManager implements DisposableBean {
    // 主虚拟线程池 - 用于一般异步任务
    private final ExecutorService mainVirtualThreadPool;
    
    // 批处理虚拟线程池 - 用于批量处理任务  
    private final ExecutorService batchVirtualThreadPool;
    
    @PreDestroy
    public void destroy() {
        // 优雅关闭所有线程池
    }
}
```

### 2. 优雅关闭机制

实现了三阶段关闭策略：

1. **停止接收新任务**: 调用 `executor.shutdown()`
2. **等待现有任务完成**: 使用 `awaitTermination()` 等待指定时间
3. **强制关闭**: 如果超时则调用 `shutdownNow()` 强制关闭

### 3. 配置化管理

支持通过配置文件自定义超时时间：

```yaml
app:
  virtual-thread:
    shutdown-timeout-seconds: 30      # 优雅关闭超时时间
    force-shutdown-timeout-seconds: 10 # 强制关闭超时时间
```

## 使用方式

### 1. 基本异步任务

```java
@Service
public class SomeService {
    @Autowired
    private VirtualThreadPoolManager virtualThreadPoolManager;
    
    public void executeAsyncTask() {
        virtualThreadPoolManager.executeAsync(() -> {
            // 异步任务逻辑
        });
    }
}
```

### 2. 返回结果的异步任务

```java
public CompletableFuture<String> processAsync() {
    return virtualThreadPoolManager.supplyAsync(() -> {
        // 处理逻辑
        return "处理结果";
    });
}
```

### 3. 批处理任务

```java
public void executeBatchTasks() {
    virtualThreadPoolManager.executeBatchAsync(() -> {
        // 批处理逻辑
    });
}
```

## 迁移指南

### 原有代码迁移

**迁移前:**
```java
private static final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

executor.execute(() -> {
    // 任务逻辑
});
```

**迁移后:**
```java
@Autowired
private VirtualThreadPoolManager virtualThreadPoolManager;

virtualThreadPoolManager.executeAsync(() -> {
    // 任务逻辑
});
```

### CompletableFuture迁移

**迁移前:**
```java
CompletableFuture.runAsync(() -> {
    // 任务逻辑
}, executor);
```

**迁移后:**
```java
CompletableFuture.runAsync(() -> {
    // 任务逻辑
}, virtualThreadPoolManager.getMainExecutor());
```

## 监控和日志

系统会在关闭过程中输出详细的日志信息：

```
INFO  - 开始关闭虚拟线程池...
INFO  - 正在关闭主虚拟线程池...
INFO  - 主虚拟线程池正常关闭成功
INFO  - 正在关闭批处理虚拟线程池...
INFO  - 批处理虚拟线程池正常关闭成功
INFO  - 虚拟线程池关闭完成
```

## 最佳实践

1. **统一使用管理器**: 避免直接创建 `ExecutorService`，统一使用 `VirtualThreadPoolManager`
2. **合理设置超时时间**: 根据业务特点调整关闭超时时间
3. **任务可中断设计**: 确保长时间运行的任务能够响应中断信号
4. **监控任务状态**: 在关键任务中添加适当的日志和状态跟踪

## 注意事项

1. 虚拟线程池管理器会在Spring容器关闭时自动触发优雅关闭
2. 如果任务无法在指定时间内完成，会被强制中断
3. 建议在任务中适当处理 `InterruptedException`
4. 对于关键业务数据，建议在任务中实现检查点机制
