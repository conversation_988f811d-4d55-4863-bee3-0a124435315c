package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.paper.PaperChapterReferenceVersionPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷章节引用版本Mapper
 * @TableName paper_chapter_reference_version
 */
@Mapper
public interface PaperChapterReferenceVersionPOMapper {

    /**
     * 根据试卷ID和章节版本ID查询
     * @param paperId 试卷ID
     * @param chapterVersionId 章节版本ID
     * @return 试卷章节引用版本对象
     */
    PaperChapterReferenceVersionPO selectByPaperAndChapterVersion(
            @Param("paperId") String paperId,
            @Param("chapterVersionId") Long chapterVersionId);

    /**
     * 根据试卷ID查询章节版本中的试卷引用
     * @param paperIds 试卷ID列表
     * @return 试卷章节引用版本对象列表
     */
    List<PaperChapterReferenceVersionPO> selectByPaperId(@Param("paperIds") List<String> paperIds);

    /**
     * 插入记录
     * @param paperChapterReferenceVersionPO 试卷章节引用版本对象
     * @return 插入的行数
     */
    int insert(PaperChapterReferenceVersionPO paperChapterReferenceVersionPO);

    /**
     * 批量插入记录
     * @param paperChapterReferenceVersionPOList 试卷章节引用版本对象列表
     * @return 插入的行数
     * */
    int batchInsert(List<PaperChapterReferenceVersionPO> paperChapterReferenceVersionPOList);

    List<PaperChapterReferenceVersionPO> selectByChapterVersionId(@Param("chapterVersionIds") List<Long> chapterVersionIds);
}
