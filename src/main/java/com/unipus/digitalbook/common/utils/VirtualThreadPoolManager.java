package com.unipus.digitalbook.common.utils;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 虚拟线程池管理器
 * 提供统一的虚拟线程池管理和优雅关闭机制
 */
@Component
@Slf4j
public class VirtualThreadPoolManager implements DisposableBean {

    // 关闭超时时间（秒），默认30秒
    private static final long shutdownTimeoutSeconds = 30L;

    // 强制关闭超时时间（秒），默认10秒
    private static final long forceShutdownTimeoutSeconds = 10L;

    // 主虚拟线程池 - 用于一般异步任务
    private final ExecutorService mainVirtualThreadPool;

    // 批处理虚拟线程池 - 用于批量处理任务
    private final ExecutorService batchVirtualThreadPool;

    public VirtualThreadPoolManager() {
        this.mainVirtualThreadPool = Executors.newVirtualThreadPerTaskExecutor();
        this.batchVirtualThreadPool = Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * 获取主虚拟线程池
     */
    public ExecutorService getMainExecutor() {
        return mainVirtualThreadPool;
    }

    /**
     * 获取批处理虚拟线程池
     */
    public ExecutorService getBatchExecutor() {
        return batchVirtualThreadPool;
    }

    /**
     * 提交异步任务到主线程池
     */
    public void executeAsync(Runnable task) {
        mainVirtualThreadPool.execute(task);
    }

    /**
     * 提交异步任务到主线程池并返回CompletableFuture
     * @param supplier 任务逻辑
     * @return CompletableFuture 对象
     */
    public <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, mainVirtualThreadPool);
    }

    /**
     * 提交异步任务到批处理线程池
     * @param task 任务
     */
    public void executeBatchAsync(Runnable task) {
        batchVirtualThreadPool.execute(task);
    }

    /**
     * 提交异步任务到批处理线程池并返回CompletableFuture
     * @param supplier 任务逻辑
     * @return CompletableFuture 对象
     */
    public <T> CompletableFuture<T> supplyBatchAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, batchVirtualThreadPool);
    }

    /**
     * 优雅关闭所有线程池
     */
    @PreDestroy
    @Override
    public void destroy() {
        // 关闭主线程池
        shutdownExecutorGracefully("Main virtual thread pool", mainVirtualThreadPool);

        // 关闭批处理线程池
        shutdownExecutorGracefully("Batch virtual thread pool", batchVirtualThreadPool);

        log.info("Virtual thread pool shutdown completed");
    }

    /**
     * 优雅关闭单个ExecutorService
     * @param poolName 线程池名称
     * @param executor 线程池对象
     */
    private void shutdownExecutorGracefully(String poolName, ExecutorService executor) {
        try {
            // 1. 停止接收新任务
            executor.shutdown();

            // 2. 等待现有任务完成
            if (executor.awaitTermination(shutdownTimeoutSeconds, TimeUnit.SECONDS)) {
                return;
            }
            log.warn("{} failed to shutdown within {} seconds, attempting force shutdown", poolName, shutdownTimeoutSeconds);

            // 3. 强制关闭
            executor.shutdownNow();

            // 4. 再次等待强制关闭完成
            if (!executor.awaitTermination(forceShutdownTimeoutSeconds, TimeUnit.SECONDS)) {
                log.error("{} force shutdown failed", poolName);
            }
        } catch (InterruptedException e) {
            log.warn("{} shutdown process was interrupted", poolName);
            Thread.currentThread().interrupt();
            // 确保强制关闭
            executor.shutdownNow();
        }
    }

}
