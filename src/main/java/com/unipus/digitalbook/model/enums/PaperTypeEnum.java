package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 试卷类型枚举类
 * 定义系统支持的三种试卷类型及其属性
 */
@Getter
public enum PaperTypeEnum {
    /** 常规卷：标准的试卷类型，包含固定的题目列表 */
    REGULAR(1, "regular", "常规卷"),
    /** 挑战卷：从题库中随机抽取题目的试卷类型 */
    CHALLENGE(2, "challenge", "挑战卷"),
    /** 诊断卷：支持诊断和推荐两种模式的试卷类型 */
    DIAGNOSTIC(3, "diagnostic", "诊断卷");

    /** 类型编码 */
    private final Integer code;
    /** 类型名称 */
    private final String name;
    /** 类型描述 */
    private final String desc;

    /**
     * 构造方法
     * @param code 类型编码
     * @param name 类型名称
     * @param desc 类型描述
     */
    PaperTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举对象
     * @param code 类型编码
     * @return 对应的枚举对象，如果未找到则返回null
     */
    public static PaperTypeEnum getByCode(Integer code) {
        if(code == null) {
            return null;
        }
        for (PaperTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 判断给定编码是否与当前枚举匹配
     * @param code 待匹配的编码
     * @return 匹配返回true，否则返回false
     */
    public Boolean match(Integer code){
        return this.getCode().equals(code);
    }

    /**
     * 判断给定枚举是否与当前枚举匹配
     * @param type 待匹配的枚举对象
     * @return 匹配返回true，否则返回false
     */
    public Boolean match(PaperTypeEnum type){
        return type==null ? Boolean.FALSE : this.getCode().equals(type.getCode());
    }

    /**
     * 根据试卷类型编码获取对应的题组类型枚举
     * @param code 试卷类型编码
     * @return 对应的题组类型枚举
     * @throws IllegalArgumentException 当编码无效时抛出异常
     */
    public static QuestionGroupTypeEnum getQuestionGroupTypeByCode(Integer code) {
        if(code == 1) {
            return QuestionGroupTypeEnum.REGULAR_PAPER;
        }else
        if(code == 2) {
            return QuestionGroupTypeEnum.CHALLENGE_PAPER;
        }else
        if(code == 3) {
            return QuestionGroupTypeEnum.DIAGNOSTIC;
        }else {
            throw new IllegalArgumentException("Invalid code for QuestionGroupType: " + code);
        }
    }

    /**
     * 根据试卷类型编码获取对应的题组类型编码
     * @param code 试卷类型编码
     * @return 对应的题组类型编码，如果输入为null则返回null
     */
    public static Integer getQuestionGroupTypeCodeByCode(Integer code) {
        if(code == null) {
            return null;
        }
        QuestionGroupTypeEnum questionGroupType = getQuestionGroupTypeByCode(code);
        return questionGroupType != null ? questionGroupType.getCode() : null;
    }

    /**
     * 根据题组类型编码获取对应的试卷类型枚举
     * @param groupCode 题组类型编码
     * @return 对应的试卷类型枚举
     * @throws IllegalArgumentException 当编码无效时抛出异常
     */
    public static PaperTypeEnum getTypeByGroupCode(Integer groupCode){
        if(QuestionGroupTypeEnum.REGULAR_PAPER.match(groupCode)) {
            return REGULAR;
        }else
        if(QuestionGroupTypeEnum.CHALLENGE_PAPER.match(groupCode)) {
            return CHALLENGE;
        }else
        if(QuestionGroupTypeEnum.DIAGNOSTIC.match(groupCode)) {
            return DIAGNOSTIC;
        }else {
            throw new IllegalArgumentException("Invalid code for QuestionGroupType: " + groupCode);
        }
    }
}
