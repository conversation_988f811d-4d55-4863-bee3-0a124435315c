package com.unipus.digitalbook.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeDTO;
import com.unipus.digitalbook.model.po.WordPracticePO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 词汇学练
 *
 * <AUTHOR>
 * @date 2025年06月27日 10:28:16
 */
@Data
public class WordPractice implements Serializable {

    /**
     * 主键ID，唯一标识一条词汇学练记录
     */
    private Long id;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 上级ID
     */
    private String parentId;

    /**
     * 词汇学练的名称
     */
    private String name;

    /**
     * 记录创建的时间
     */
    @JsonFormat(pattern = "MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 记录最后更新的时间
     */
    private Date updateTime;

    /**
     * 创建该记录的用户ID
     */
    private Long createBy;

    /**
     * 最后更新该记录的用户ID
     */
    private Long updateBy;

    /**
     * 记录是否有效，0 表示无效，1 表示有效
     */
    private Boolean enable;
    /**
     * 教材名称
     */
    private String bookName;
    /**
     * 内容數據
     */
    private String jsonData;
    /**
     * 教材ID
     */
    private String bookId;
    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 转换为持久化对象
     *
     * @return 返回WordPracticePO对象
     */
    public WordPracticePO toPO() {
        WordPracticePO po = new WordPracticePO();
        po.setId(this.id);
        po.setBizId(this.bizId);
        po.setParentId(this.parentId);
        po.setName(this.name);
        po.setCreateTime(this.createTime);
        po.setUpdateTime(this.updateTime);
        po.setCreateBy(this.createBy);
        po.setUpdateBy(this.updateBy);
        po.setEnable(this.enable);
        return po;
    }

    /**
     * 转换为数据传输对象
     *
     * @return 返回WordPracticeDTO对象
     */
    public WordPracticeDTO toDTO() {
        WordPracticeDTO dto = new WordPracticeDTO();
        dto.setId(this.id);
        dto.setBizId(this.bizId);
        dto.setName(this.name);
        dto.setBookName(this.bookName);
        dto.setJsonData(this.jsonData);
        return dto;
    }
}
