package com.unipus.digitalbook.service.useraction.strategy.content;

import com.alibaba.fastjson2.TypeReference;
import com.google.common.base.Stopwatch;
import com.unipus.digitalbook.dao.ChapterVersionPOMapper;
import com.unipus.digitalbook.dao.UserChapterProgressPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.*;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.model.po.action.UserChapterProgressPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.publisher.UserActionPublisher;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.useraction.strategy.node.NodeCompletionStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class UserChapterAction implements UserContentAction {

    @Resource
    private NodeCompletionStrategyFactory nodeCompletionStrategyFactory;
    @Resource
    private UserActionPublisher userActionPublisher;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ChapterService chapterService;

    @Resource
    private UserChapterProgressPOMapper userChapterProgressPOMapper;

    @Resource
    private BookVersionService bookVersionService;
    @Resource
    private TenantMessageProducer tenantMessageProducer;

    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;

    @Override
    public Set<ContentTypeEnum> getTypes() {
        return Set.of(ContentTypeEnum.CHAPTER);
    }

    @Override
    public Response<Boolean> finishNode(UserAction userAction) {
        Long chapterVersionId = userAction.getContentVersionId();
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        String nodeId = userAction.getNodeId();
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.error("节点不存在, 或者已经变更 nodeId = {}", nodeId);
            return Response.fail("节点不存在");
        }
        return doFinishNode(new ContentNode(chapterNode), userAction);
    }

    @Override
    public Response<Boolean> finishQuestionNode(UserAction userAction) {
        Long chapterVersionId = userAction.getContentVersionId();
        Map<String, ChapterNode> nodeMap = chapterService.getQuestionNodeByChapterVersionId(chapterVersionId);
        String questionId = userAction.getQuestionId();
        ChapterNode chapterNode = nodeMap.get(questionId);
        if (chapterNode == null) {
            log.error("题目不存在, 或者已经变更 questionId = {}", questionId);
            return Response.fail("题目不存在");
        }
        ContentNode node = new ContentNode(chapterNode);
        String progressKey = getProgressKey(userAction.getTenantId(), userAction.getOpenId(), userAction.getContentVersionId());
        setFinishNode(progressKey, node.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> postProcessNode(UserAction userAction, ContentNode contentNode) {
        saveProgress(userAction);
        Long chapterVersionId = userAction.getContentVersionId();
        String chapterId = userAction.getContentId();
        String openId = userAction.getOpenId();
        // 如果章节发布的版本和写入的版本不一致，需要双写
        Long targetChapterVersionId = getTargetChapterVersionId(userAction.getTenantId(), chapterId);
        if (targetChapterVersionId != null && !Objects.equals(targetChapterVersionId, chapterVersionId)) {
            log.debug("diff chapter version openId:{} chapterId:{}, chapterVersionId: {}, {}", openId, chapterId, chapterVersionId, targetChapterVersionId);
            directFinishNode(userAction.getTenantId(), openId, chapterId, targetChapterVersionId, contentNode.getId());
        }
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        ChapterNode chapterNode = nodeMap.get(contentNode.getId());
        // todo 待优化 通过租户配置 判断推送条件
        if (userAction.getTenantId() == 1) {
            syncThirdForUai(userAction, contentNode);
            return Response.success(true);
        }
        byte[] progressValue = getProgressValue(userAction.getTenantId(), openId, chapterVersionId);
        List<NodeProgress> nodeProgresses = completionStatus(chapterNode, nodeMap, progressValue);
        int total = nodeMap.size();
        int finishedNodeCount = getFinishedNodeCount(userAction.getTenantId(), openId, chapterVersionId);
        log.debug("total:{}, finishedNodeCount:{}", total, finishedNodeCount);
        nodeProgresses.forEach(nodeProgress -> {
            syncThird(userAction, nodeProgress, total, finishedNodeCount);
        });
        return Response.success(true);
    }

    private List<NodeProgress> completionStatus(ChapterNode node, Map<String, ChapterNode> nodeMap, byte[] progressValue) {
        List<NodeProgress> nodeProgresses = new ArrayList<>();
        nodeProgresses.add(new NodeProgress(node.getId(), node.getType(), 1, 1));
        ChapterNode current = nodeMap.get(node.getParentId());
        int total = 0;
        int completedCount = 0;
        int maxLevel = 6;
        while (current != null && maxLevel > 0) {
            maxLevel--;
            total += current.getChildren().size();
            if (isCompletedNode(progressValue, current.getOffset())) {
                completedCount += current.getChildren().size();
                current = nodeMap.get(current.getParentId());
                continue;
            }
            completedCount += (int) current.getChildren().stream().filter(c -> isCompletedNode(progressValue, c.getOffset())).count();
            nodeProgresses.add(new NodeProgress(current.getId(), current.getType(), total, completedCount));
            current = nodeMap.get(current.getParentId());
        }
        return nodeProgresses;
    }

    @Override
    public Response<Boolean> postPublishProcess(Long tenantId, String contentId, Long contentVersionId) {
        // 获取当前学习的章节版本
        Long originalChapterVersionId = getTargetChapterVersionId(tenantId, contentId);
        if (!Objects.equals(originalChapterVersionId, contentVersionId)) {
            log.debug("diff chapter versionId: {},  {}, {}", contentId, originalChapterVersionId, contentVersionId);
            stringRedisTemplate.opsForValue().set(getTargetChapterVersionKey(tenantId, contentId), contentVersionId.toString());
            // 开始迁移数据
            migratedProgress(tenantId, contentId, originalChapterVersionId, contentVersionId);
        }
        return Response.success(true);
    }

    private void syncThirdForUai(UserAction userAction, ContentNode contentNode) {
        if (contentNode.isRealQuestionNode()) {
            return;
        }
        Long chapterVersionId = userAction.getContentVersionId();
        BookVersion bookVersion = bookVersionService.getBookVersionByChapterVersionId(chapterVersionId);
        log.info("sync book version: {}, {}, {}", bookVersion.getBookId(), bookVersion.getVersionNum(), chapterVersionId);

        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAction.getTenantId(), MessageTopicEnum.PUSH_USER_PROGRESS)).isPresent()) {
            CompletableFuture<Response<String>> responseFuture = tenantMessageProducer.produceAsyncMessage(
                    userAction.getTenantId(),
                    MessageTopicEnum.PUSH_USER_PROGRESS,
                    new TypeReference<>() {
                    },
                    new UserContentProgressData(userAction, new NodeProgress(contentNode.getId(), contentNode.getType(), 1, 1)),
                    new TypeReference<>() {});
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_PROGRESS.name(), userAction.getTenantId());
        }
    }
    private void syncThird(UserAction userAction, NodeProgress nodeProgress, int total, int completed) {
        Long chapterVersionId = userAction.getContentVersionId();
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAction.getTenantId(), MessageTopicEnum.PUSH_USER_PROGRESS)).isPresent()) {
            tenantMessageProducer.produceAsyncMessage(
                    userAction.getTenantId(),
                    MessageTopicEnum.PUSH_USER_PROGRESS,
                    new TypeReference<Response<String>>() {
                    },
                    new UserContentProgressData(userAction, nodeProgress, total, completed),
                    new TypeReference<>() {});
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_PROGRESS.name(), userAction.getTenantId());
        }
    }
    private Long getTargetChapterVersionId(Long tenantId, String chapterId) {
        String s = stringRedisTemplate.opsForValue().get(getTargetChapterVersionKey(tenantId, chapterId));
        if (!StringUtils.hasText(s)) {
            return null;
        }
        return Long.valueOf(s);
    }

    private Response<Boolean> directFinishNode(Long tenantId, String openId, String chapterId, Long chapterVersionId, String nodeId) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.debug("节点可能已变更 {}", nodeId);
            return Response.fail("节点可能已变更");
        }
        setFinishNode(getProgressKey(tenantId, openId, chapterVersionId), chapterNode.getOffset());
        byte[] bit = getProgressValue(tenantId, openId, chapterVersionId);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, chapterVersionId, bit);
        return Response.success(true);
    }

    /**
     * 持久化进度
     * @param userAction
     * @return
     */
    private Response<Boolean> saveProgress(UserAction userAction) {
        Long tenantId = userAction.getTenantId();
        String openId = userAction.getOpenId();
        Long chapterVersionId = userAction.getContentVersionId();
        String chapterId = userAction.getContentId();
        byte[] bit = getProgressValue(tenantId, openId, chapterVersionId);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, chapterVersionId, bit);
        return Response.success(true);
    }

    private Response<Boolean> migratedProgress(Long tenantId, String chapterId, Long originChapterVersionId, Long chapterVersionId) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapCache(chapterVersionId);
        Map<String, ChapterNode> nodeMapOrigin = chapterService.getNodeMapCache(originChapterVersionId);
        log.debug("migrate chapterId: {}, {}, {}", chapterId, originChapterVersionId, chapterVersionId);
        // 获取章节下所有用户当前章节的进度数据
        Long lastId = 0L;
        List<UserChapterProgressPO> progressList = userChapterProgressPOMapper.getProgressList(tenantId, originChapterVersionId, lastId);
        while (!progressList.isEmpty()) {
            progressList.forEach(progress -> {
                // 开始迁移用户章节数据
                migrateUserProgress(tenantId, progress.getOpenId(), chapterId, originChapterVersionId, nodeMapOrigin, chapterVersionId, nodeMap);
            });
            log.debug("progressList size: {}, {}", progressList.size(), lastId);
            lastId = progressList.getLast().getId();
            progressList = userChapterProgressPOMapper.getProgressList(tenantId, originChapterVersionId, lastId);
        }
        return Response.success();
    }

    private void migrateUserProgress(Long tenantId, String openId, String chapterId, Long originChapterVersionId, Map<String, ChapterNode> nodeMapOrigin, Long targetChapterVersionId, Map<String, ChapterNode> nodeMap) {
        log.debug("migrate user progress: {}, {}, {}, {}, {}", tenantId, openId, chapterId, originChapterVersionId, targetChapterVersionId);
        byte[] progressValue = getProgressValue(tenantId, openId, originChapterVersionId);
        Set<String> completedList = new ReadProgressList().computation(progressValue, nodeMapOrigin).getCompletedList();
        if (completedList == null || completedList.isEmpty()) {
            log.debug("no progress found for openId: {}, chapterId: {}, originChapterVersionId: {}", openId, chapterId, originChapterVersionId);
            return;
        }
        String progressKey = getProgressKey(tenantId, openId, targetChapterVersionId);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            completedList.stream().parallel().forEach(nodeId -> {
                ChapterNode chapterNode = nodeMap.get(nodeId);
                if (chapterNode != null) {
                    stringRedisTemplate.opsForValue().setBit(progressKey, chapterNode.getOffset(), true);
                }
            });
            return true;
        });
        byte[] bit = getProgressValue(tenantId, openId, targetChapterVersionId);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, targetChapterVersionId, bit);
    }

    private Response<Boolean> doFinishNode(ContentNode node, UserAction userAction) {
        String progressKey = getProgressKey(userAction.getTenantId(), userAction.getOpenId(), userAction.getContentVersionId());
        boolean completed = nodeCompletionStrategyFactory.getStrategy(node.getType()).isCompleted(node, userAction);
        if (!completed) {
            log.debug("节点未完成 {}", node.getId());
            return Response.success(false);
        }
        setFinishNode(progressKey, node.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    private byte[] getProgressValue(Long tenantId, String openId, Long chapterVersionId) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        return stringRedisTemplate.execute((RedisCallback<byte[]>) connection ->
                connection.stringCommands().get(progressRawKey)
        );
    }

    private int getFinishedNodeCount(Long tenantId, String openId, Long chapterVersionId) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        Long count = stringRedisTemplate.execute(
                (RedisCallback<Long>) connection -> connection.bitCount(progressRawKey)
        );
        return count.intValue();
    }

    private Boolean setFinishNode(String key, int offset) {
        stringRedisTemplate.opsForValue().setBit(key, offset, true);
        return true;
    }

    private String getProgressKey(Long tenantId, String openId, Long chapterVersionId) {
        return String.format("progress:%d:%s:%d", tenantId, openId, chapterVersionId);
    }

    private String getTargetChapterVersionKey(Long tenantId, String chapterId) {
        return String.format("targetChapterVersion:%d:%s", tenantId, chapterId);
    }

    private boolean isCompletedNode(String key, Integer offset) {
        return stringRedisTemplate.opsForValue().getBit(key, offset);
    }
}
