package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.common.utils.UserUtil;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.dto.wordpractice.WordPracticeListDTO;
import com.unipus.digitalbook.model.entity.WordPractice;
import com.unipus.digitalbook.model.entity.wordpractice.WordPracticeSearchList;
import com.unipus.digitalbook.model.po.WordPracticeInstancePO;
import com.unipus.digitalbook.model.po.WordPracticePO;
import com.unipus.digitalbook.model.po.book.BookBasicPO;
import com.unipus.digitalbook.model.po.book.BookSettingLogPO;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import com.unipus.digitalbook.service.WordPracticeService;
import com.unipus.digitalbook.service.remote.restful.wordpractice.WordPracticeApiService;
import com.unipus.digitalbook.service.remote.restful.wordpractice.model.*;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.security.Key;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 词汇学练服务实现类
 *
 * <AUTHOR>
 * @date 2025年06月27日 10:55:16
 */
@Slf4j
@Service
public class WordPracticeServiceImpl implements WordPracticeService {

    @Resource
    private WordPracticePOMapper wordPracticePOMapper;
    @Resource
    private WordPracticeInstancePOMapper wordPracticeInstancePOMapper;
    @Resource
    private BookBasicPOMapper bookBasicPOMapper;
    @Resource
    private ChapterPOMapper chapterPOMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WordPracticeApiService wordPracticeApiService;
    @Resource
    private BookSettingLogPOMapper bookSettingLogPOMapper;
    @Value("${remote.wordpractice.secret}")
    private String wordPracticeTokenSecret;

    @Override
    public WordPracticeSearchList searchList(String parentId, int offset, int limit) {
        WordPracticeSearchList wordPracticeSearchList = new WordPracticeSearchList();
        List<WordPractice> list = wordPracticePOMapper.selectList(parentId, offset, limit);
        int total = wordPracticePOMapper.selectCount(parentId);
        wordPracticeSearchList.setTotalCount(total);
        wordPracticeSearchList.setWordPracticeList(list);
        return wordPracticeSearchList;
    }

    @Override
    public String addWordPractice(WordPractice wordPractice) {
        log.info("[词汇学练-添加]addWordPractice开始执行");
        // 获取当前用户的ID
        Long currentUser = UserUtil.getCurrentUser();
        WordPracticePO wordPracticePO = wordPractice.toPO();
        wordPracticePO.setCreateBy(currentUser);
        wordPracticePO.setUpdateBy(currentUser);
        // 校验名称重复
        Integer exist = wordPracticePOMapper.selectByName(wordPractice.getParentId(), wordPractice.getName(), null);
        if (Objects.nonNull(exist) && exist > 0) {
            throw new BizException("所选章节下的词汇学练名称重复");
        }
        log.info("[词汇学练-添加]调用词汇学练接口开始");
        // 调用词汇学练接口创建并返回编辑态id
        WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.saveQuestion(new AddWordPracticeRequest(wordPractice.getName(), JSON.parseObject(wordPractice.getJsonData())), getApiRequestHeader());
        log.info("[词汇学练-添加]调用词汇学练接口结束 result:{}", JsonUtil.toJsonString(wordPracticeApiResponse));
        boolean result = wordPracticeApiResponse.isSuccess();
        if (result) {
            // 编辑态id
            String instanceId = (String) wordPracticeApiResponse.getValue();
            // 插入词汇学练信息到数据库
            wordPracticePOMapper.insertSelective(wordPracticePO);
            // 插入词汇学练实例
            WordPracticeInstancePO wordPracticeInstancePO = new WordPracticeInstancePO(wordPracticePO.getId(), instanceId, wordPracticePO.getName(), 2, currentUser);
            wordPracticeInstancePOMapper.insertSelective(wordPracticeInstancePO);
            log.info("[词汇学练-添加]插入数据完成 result:{}", JsonUtil.toJsonString(wordPracticePO));
            // 返回新插入记录的主键ID
            return wordPracticePO.getId().toString();
        } else {
            throw new BizException("添加失败");
        }

    }

    @Override
    public boolean editWordPractice(WordPractice wordPractice) {
        WordPracticeInstancePO wordPracticeInstancePO = wordPracticeInstancePOMapper.selectEditStatusInstance(wordPractice.getId());
        // 编辑态id
        String editId = wordPracticeInstancePO.getInstanceId();
        // 内容json
        String jsonData = wordPractice.getJsonData();
        // 校验名称重复
        Integer exist = wordPracticePOMapper.selectByName(wordPractice.getParentId(), wordPractice.getName(), wordPractice.getId());
        if (Objects.nonNull(exist) && exist > 0) {
            throw new BizException("所选章节下的词汇学练名称重复");
        }
        // 调用词汇学练接口保存
        log.info("[词汇学练-编辑]调用词汇学练接口开始");
        WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.updateQuestion(new UpdateWordPracticeRequest(editId, wordPractice.getName(), JSON.parseObject(jsonData)), getApiRequestHeader());
        log.info("[词汇学练-编辑]调用词汇学练接口结束 result:{}", JsonUtil.toJsonString(wordPracticeApiResponse));
        boolean result = wordPracticeApiResponse.isSuccess();
        // 调用成功后更新数据库数据
        if (result) {
            wordPracticeInstancePO.setName(wordPractice.getName());
            wordPracticeInstancePOMapper.updateByPrimaryKeySelective(wordPracticeInstancePO);
            WordPracticePO wordPracticePO = wordPractice.toPO();
            wordPracticePO.setUpdateBy(UserUtil.getCurrentUser());
            wordPracticePOMapper.updateByPrimaryKeySelective(wordPracticePO);
            return true;
        } else {
            throw new BizException("编辑失败");
        }
    }

    @Override
    public WordPractice getEditStatusWordPracticeById(Long id) {
        WordPracticePO wordPracticePO = wordPracticePOMapper.selectByPrimaryKey(id);
        WordPractice wordPractice = wordPracticePO.toEntity();
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(wordPracticePO.getBizId());
        if (Objects.nonNull(chapterPO)) {
            BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookId(chapterPO.getBookId());
            if (Objects.nonNull(bookBasicPO)) {
                wordPractice.setBookName(bookBasicPO.getChineseName());
            }
        }
        // 编辑态id
        WordPracticeInstancePO wordPracticeInstancePO = wordPracticeInstancePOMapper.selectEditStatusInstance(id);
        String editStatusId = wordPracticeInstancePO.getInstanceId();
        // 调用词汇学练接口获取内容数据
        log.info("[词汇学练-详情]调用词汇学练接口开始");
        WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.detail(new DetailWordPracticeRequest(editStatusId), getApiRequestHeader());
        log.info("[词汇学练-详情]调用词汇学练接口结束 result:{}", JsonUtil.toJsonString(wordPracticeApiResponse));
        boolean result = wordPracticeApiResponse.isSuccess();
        // 调用成功后删除数据库数据
        if (result) {
            Map map =  (Map)wordPracticeApiResponse.getValue();
            String jsonData = JSON.toJSONString(map.get("detail"));
            wordPractice.setJsonData(jsonData);
            return wordPractice;
        } else {
            throw new BizException("获取详情失败");
        }
    }

    @Override
    public WordPractice getPublishStatusWordPracticeById(String bizId) {
        WordPracticePO wordPracticePO = wordPracticePOMapper.selectByBizId(bizId);
        WordPractice wordPractice = wordPracticePO.toEntity();
        ChapterPO chapterPO = chapterPOMapper.selectByPrimaryKey(wordPracticePO.getBizId());
        if (Objects.nonNull(chapterPO)) {
            BookBasicPO bookBasicPO = bookBasicPOMapper.selectByBookId(chapterPO.getBookId());
            if (Objects.nonNull(bookBasicPO)) {
                wordPractice.setBookName(bookBasicPO.getChineseName());
            }
        }
        // 查询发布态
        WordPracticeInstancePO wordPracticeInstancePO = wordPracticeInstancePOMapper.selectPublishStatusInstance(wordPracticePO.getId());
        if (Objects.isNull(wordPracticeInstancePO)) {
            throw new IllegalArgumentException("该词汇学练尚未发布");
        }
        String publishStatusId = wordPracticeInstancePO.getInstanceId();

        // todo 调用词汇学练接口获取内容数据
        String jsonData = "";
        wordPractice.setJsonData(jsonData);
        wordPractice.setName(wordPracticeInstancePO.getName());
        return wordPractice;
    }

    @Override
    public boolean deleteWordPractice(Long id) {
        WordPracticePO wordPracticePO = wordPracticePOMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wordPracticePO)) {
            throw new IllegalArgumentException("词汇学练不存在");
        }
        WordPracticeInstancePO editInstancePO = wordPracticeInstancePOMapper.selectEditStatusInstance(id);
        // 编辑态id
        String editStatusId = editInstancePO.getInstanceId();
        //WordPracticeInstancePO publishInstancePO = wordPracticeInstancePOMapper.selectPublishStatusInstance(id);
        // 发布态id
        //String publishStatusId = Objects.isNull(publishInstancePO) ? null : publishInstancePO.getInstanceId();
        // 调用词汇学练接口删除
        log.info("[词汇学练-删除]调用词汇学练接口开始");
        WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.delQuestion(new DelWordPracticeRequest(editStatusId), getApiRequestHeader());
        log.info("[词汇学练-删除]调用词汇学练接口结束 result:{}", JsonUtil.toJsonString(wordPracticeApiResponse));
        boolean result = wordPracticeApiResponse.isSuccess();
        // 调用成功后删除数据库数据
        if (result) {
            wordPracticePOMapper.deleteByPrimaryKey(id);
            wordPracticeInstancePOMapper.deleteByPracticeId(id);
        } else {
            throw new BizException("删除失败");
        }
        return true;
    }

    @Override
    public boolean publishBatch(String bookId, List<String> parentIds, Long logId) {
        Long currentUser = UserUtil.getCurrentUser();
        List<WordPracticePO> wordPracticePOS = null;
        if (StringUtils.isNotBlank(bookId)) {
            List<ChapterPO> chapterPOS = chapterPOMapper.selectByBookId(bookId);
            if (CollectionUtils.isEmpty(chapterPOS)) {
                throw new IllegalArgumentException("该书籍没有章节信息");
            }
            // 获取所有章节id
            List<String> chapterIdList = chapterPOS.stream()
                    .map(ChapterPO::getId)
                    .toList();
            // 查询所有章节下的词汇学练
            wordPracticePOS = wordPracticePOMapper.selectListByParentIds(chapterIdList);

            // 发布设置日志
            if (Objects.nonNull(logId)) {
                bookSettingLogPOMapper.updateWordPracticeUpdated(logId);
            } else {
                Date now = Calendar.getInstance().getTime();
                BookSettingLogPO bookSettingLogPO = new BookSettingLogPO();
                bookSettingLogPO.setBookId(bookId);
                bookSettingLogPO.setWordPracticeUpdated(true);
                bookSettingLogPO.setKnowledgeUpdated(false);
                bookSettingLogPO.setCreateTime(now);
                bookSettingLogPO.setUpdateTime(now);
                bookSettingLogPO.setCreateBy(currentUser);
                bookSettingLogPO.setUpdateBy(currentUser);
                bookSettingLogPO.setEnable(true);
                bookSettingLogPOMapper.saveLog(bookSettingLogPO);
            }
        } else if (!CollectionUtils.isEmpty(parentIds)) {
            // 查询所有章节下的词汇学练
            wordPracticePOS = wordPracticePOMapper.selectListByParentIds(parentIds);
        } else {
            throw new IllegalArgumentException("缺少必要参数");
        }
        if (CollectionUtils.isEmpty(wordPracticePOS)) {
            throw new IllegalArgumentException("没有可发布的词汇学练信息");
        }
        // 查询词汇学练实例
        List<WordPracticeInstancePO> wordPracticeInstancePOS = wordPracticeInstancePOMapper.selectListByPracticeIds(wordPracticePOS.stream().map(WordPracticePO::getId).toList());
        // 遍历全部词汇学练
        wordPracticePOS.forEach(wordPracticePO -> {
            // 编辑态
            WordPracticeInstancePO editStatusInstance = findInstanceByStatus(wordPracticeInstancePOS, wordPracticePO.getId(), 2);
            // 发布态
            WordPracticeInstancePO publishStatusInstance = findInstanceByStatus(wordPracticeInstancePOS, wordPracticePO.getId(), 1);
            // 调用词汇学练接口发布
            if (Objects.isNull(publishStatusInstance)) {
                // 未发布过调用copy接口发布并返回id
                WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.publishQuestion(new PublishWordPracticeRequest(editStatusInstance.getInstanceId()), getApiRequestHeader());
                if (!wordPracticeApiResponse.isSuccess()) {
                    log.error("词汇学练发布失败，id：{},instanceId：{},msg：{}", wordPracticePO.getId(), editStatusInstance.getInstanceId(), wordPracticeApiResponse.getMsg());
                    return;
                }
                // 保存发布实例
                String returnId = wordPracticeApiResponse.getValue().toString();
                WordPracticeInstancePO insertInstancePO = new WordPracticeInstancePO(wordPracticePO.getId(), returnId, editStatusInstance.getName(), 1, currentUser);
                wordPracticeInstancePOMapper.insertSelective(insertInstancePO);
            } else {
                // 已发布过调用overWrite覆盖接口
                WordPracticeApiResponse wordPracticeApiResponse = wordPracticeApiService.publishQuestion(new PublishWordPracticeRequest(editStatusInstance.getInstanceId()), getApiRequestHeader());
                if (!wordPracticeApiResponse.isSuccess()) {
                    log.error("词汇学练发布失败，id：{},instanceId：{},msg：{}", wordPracticePO.getId(), editStatusInstance.getInstanceId(), wordPracticeApiResponse.getMsg());
                }
            }
        });
        return true;
    }

    @Override
    public WordPracticeListDTO getBookAllWordPractice(String bookId) {
        // 根据教材id查询所有章节
        List<ChapterPO> chapterPOS = chapterPOMapper.selectByBookId(bookId);

        // 获取所有章节id
        List<String> chapterIdList = chapterPOS.stream()
                .map(ChapterPO::getId)
                .toList();

        // 查询所有章节下的词汇学练
        List<WordPracticePO> wordPracticePOS =
                CollectionUtils.isEmpty(chapterIdList) ? Collections.emptyList():
                wordPracticePOMapper.selectListByParentIds(chapterIdList);

        List<Long> practiceIds = wordPracticePOS.stream().map(WordPracticePO::getId).toList();

        // 查询所有词汇学练实例
        List<WordPracticeInstancePO> wordPracticeInstancePOS =
                CollectionUtils.isEmpty(practiceIds) ? Collections.emptyList():
                wordPracticeInstancePOMapper.selectListByPracticeIds(practiceIds);

        return new WordPracticeListDTO(chapterPOS, wordPracticePOS, wordPracticeInstancePOS);
    }

    @Override
    public String generateWordPracticeApiServiceToken() {
        // 查询缓存
        String tk = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_WORD_PRACTICE_API_SERVICE_TOKEN);
        if (StringUtils.isNotBlank(tk)) {
            return tk;
        }
        // 生成token
        Map<String, Object> claims = new HashMap<>();
        claims.put("schoolid", "2");
        claims.put("openid", "b46f20fa09684709a3d173df0c41e14f");

        String token = Jwts.builder()
                .claims(claims)
                .subject("")
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + 60 * 1000 * 60 * 24))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
        // 缓存
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_WORD_PRACTICE_API_SERVICE_TOKEN,
                token, CacheConstant.REDIS_USER_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        return token;
    }

    private Key getSigningKey() {
        return Keys.hmacShaKeyFor(wordPracticeTokenSecret.getBytes());
    }

    private WordPracticeInstancePO findInstanceByStatus(List<WordPracticeInstancePO> wordPracticeInstancePOS, Long practiceId, Integer status) {
        return wordPracticeInstancePOS.stream().filter(wordPracticeInstancePO -> Objects.equals(wordPracticeInstancePO.getPracticeId(), practiceId) && Objects.equals(wordPracticeInstancePO.getStatus(), status)).findFirst().orElse(null);
    }

    private Map<String, String> getApiRequestHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("token", generateWordPracticeApiServiceToken());
        return headers;
    }
}
