package com.unipus.digitalbook.conf.feishu;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 飞书机器人配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "feishu.bot")
public class FeishuBotProperties {
    
    /**
     * 飞书机器人Webhook地址
     */
    private String webhookUrl;
    
    /**
     * 飞书机器人密钥（用于签名验证）
     */
    private String secret;
    
    /**
     * 是否启用飞书机器人
     */
    private boolean enabled = true;
    
    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 10000;
    
    /**
     * Grafana基础URL
     */
    private String grafanaBaseUrl = "http://************:31588";
    
    /**
     * Grafana数据源UID
     */
    private String grafanaDatasourceUid = "ab10533a-937d-40e0-bb29-cb9ff814fe1a";
    
    /**
     * Grafana应用名称（用于日志查询）
     */
    private String grafanaAppName = "digitalbook";
    
    /**
     * Grafana查询时间范围（从当前时间往前推）
     */
    private String grafanaTimeRange = "now-2h";
    
    /**
     * Grafana查询时间范围结束（默认到当前时间）
     */
    private String grafanaTimeRangeTo = "now";
} 