<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperChapterReferenceVersionPOMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="PaperChapterReferenceVersionPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="paper_id" jdbcType="CHAR" property="paperId" />
        <result column="book_id" jdbcType="CHAR" property="bookId" />
        <result column="chapter_id" jdbcType="CHAR" property="chapterId" />
        <result column="chapter_version_id" jdbcType="BIGINT" property="chapterVersionId" />
        <result column="position" jdbcType="CHAR" property="position" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="enable" jdbcType="BIT" property="enable" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, paper_id, book_id, chapter_id, chapter_version_id, position, create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 根据试卷ID和章节版本ID查询 -->
    <select id="selectByPaperAndChapterVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from paper_chapter_reference_version
        where paper_id = #{paperId,jdbcType=CHAR}
        and chapter_version_id = #{chapterVersionId,jdbcType=BIGINT}
        and enable = 1
    </select>

    <!-- 根据试卷ID查询章节 -->
    <select id="selectByPaperId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from paper_chapter_reference_version
        where paper_id in
        <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
            #{paperId,jdbcType=CHAR}
        </foreach>
        and enable = 1
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="PaperChapterReferenceVersionPO" useGeneratedKeys="true" keyProperty="id">
        insert into paper_chapter_reference_version (
            paper_id,
            book_id,
            chapter_id,
            chapter_version_id,
            position,
            create_by
        )
        values (
            #{paperId,jdbcType=CHAR},
            #{bookId,jdbcType=CHAR},
            #{chapterId,jdbcType=CHAR},
            #{chapterVersionId,jdbcType=BIGINT},
            #{position,jdbcType=CHAR},
            #{createBy,jdbcType=BIGINT}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into paper_chapter_reference_version (
            paper_id,
            book_id,
            chapter_id,
            chapter_version_id,
            position,
            create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.paperId,jdbcType=CHAR},
            #{item.bookId,jdbcType=CHAR},
            #{item.chapterId,jdbcType=CHAR},
            #{item.chapterVersionId,jdbcType=BIGINT},
            #{item.position,jdbcType=CHAR},
            #{item.createBy,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <select id="selectByChapterVersionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from paper_chapter_reference_version
        where chapter_version_id in
        <foreach collection="chapterVersionIds" item="chapterVersionId" open="(" separator="," close=")">
            #{chapterVersionId,jdbcType=BIGINT}
        </foreach>
        and enable = 1
    </select>
</mapper>